import { MongoClient, Db, Collection, ObjectId } from 'mongodb';
import { MONGODB_CONFIG } from './config';

// 数据模型接口
export interface Document {
  _id?: ObjectId;
  id?: string; // 兼容性字段
  title: string;
  created_at: Date;
  updated_at?: Date;
}

export interface Section {
  _id?: ObjectId;
  id?: string; // 兼容性字段
  document_id: string | ObjectId;
  content: string;
  rewritten?: string;
  created_at: Date;
  updated_at?: Date;
}

export interface GeneratedImage {
  _id?: ObjectId;
  id?: string; // 兼容性字段
  document_id?: string | ObjectId;
  section_id?: string | ObjectId;
  prompt: string;
  image_url: string;
  revised_prompt?: string;
  created_at: Date;
}

// MongoDB 连接管理
class MongoDBManager {
  private client: MongoClient | null = null;
  private db: Db | null = null;
  private isConnecting = false;

  async connect(): Promise<Db> {
    if (this.db) {
      return this.db;
    }

    if (this.isConnecting) {
      // 等待连接完成
      while (this.isConnecting) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      if (this.db) return this.db;
    }

    this.isConnecting = true;

    try {
      this.client = new MongoClient(MONGODB_CONFIG.connectionString, MONGODB_CONFIG.options);
      await this.client.connect();
      this.db = this.client.db(MONGODB_CONFIG.dbName);
      
      // 创建索引以提高查询性能
      await this.createIndexes();
      
      console.log('MongoDB 连接成功');
      return this.db;
    } catch (error) {
      console.error('MongoDB 连接失败:', error);
      throw error;
    } finally {
      this.isConnecting = false;
    }
  }

  private async createIndexes(): Promise<void> {
    if (!this.db) return;

    try {
      // 文档集合索引
      await this.db.collection(MONGODB_CONFIG.collections.documents)
        .createIndex({ created_at: -1 });

      // 段落集合索引
      await this.db.collection(MONGODB_CONFIG.collections.sections)
        .createIndexes([
          { key: { document_id: 1 } },
          { key: { created_at: 1 } }
        ]);

      // 图片集合索引
      await this.db.collection(MONGODB_CONFIG.collections.images)
        .createIndexes([
          { key: { document_id: 1 } },
          { key: { section_id: 1 } },
          { key: { created_at: -1 } }
        ]);
    } catch (error) {
      console.warn('创建索引时出现警告:', error);
    }
  }

  async disconnect(): Promise<void> {
    if (this.client) {
      await this.client.close();
      this.client = null;
      this.db = null;
      console.log('MongoDB 连接已关闭');
    }
  }

  getCollection<T = any>(collectionName: string): Collection<T> {
    if (!this.db) {
      throw new Error('数据库未连接');
    }
    return this.db.collection<T>(collectionName);
  }
}

// 单例实例
const mongoManager = new MongoDBManager();

// 导出数据库操作函数
export async function createDocument(title: string): Promise<string> {
  const db = await mongoManager.connect();
  const collection = mongoManager.getCollection<Document>(MONGODB_CONFIG.collections.documents);
  
  const document: Omit<Document, '_id'> = {
    title,
    created_at: new Date(),
    updated_at: new Date()
  };

  const result = await collection.insertOne(document);
  return result.insertedId.toString();
}

export async function saveSections(documentId: string, sections: { content: string }[]): Promise<Section[]> {
  const db = await mongoManager.connect();
  const collection = mongoManager.getCollection<Section>(MONGODB_CONFIG.collections.sections);
  
  const sectionsToInsert: Omit<Section, '_id'>[] = sections.map(section => ({
    document_id: new ObjectId(documentId),
    content: section.content,
    created_at: new Date(),
    updated_at: new Date()
  }));

  const result = await collection.insertMany(sectionsToInsert);
  
  // 返回插入的文档，包含生成的 ID
  const insertedSections = await collection.find({
    _id: { $in: Object.values(result.insertedIds) }
  }).toArray();

  return insertedSections.map(section => ({
    ...section,
    id: section._id?.toString(),
    document_id: section.document_id.toString()
  }));
}

export async function updateSection(id: string, rewritten: string): Promise<void> {
  const db = await mongoManager.connect();
  const collection = mongoManager.getCollection<Section>(MONGODB_CONFIG.collections.sections);
  
  await collection.updateOne(
    { _id: new ObjectId(id) },
    { 
      $set: { 
        rewritten,
        updated_at: new Date()
      }
    }
  );
}

export async function getSections(documentId: string): Promise<Section[]> {
  const db = await mongoManager.connect();
  const collection = mongoManager.getCollection<Section>(MONGODB_CONFIG.collections.sections);
  
  const sections = await collection.find({
    document_id: new ObjectId(documentId)
  }).sort({ created_at: 1 }).toArray();

  return sections.map(section => ({
    ...section,
    id: section._id?.toString(),
    document_id: section.document_id.toString()
  }));
}

export async function getLatestDocument(): Promise<Section[]> {
  const db = await mongoManager.connect();
  const documentsCollection = mongoManager.getCollection<Document>(MONGODB_CONFIG.collections.documents);
  const sectionsCollection = mongoManager.getCollection<Section>(MONGODB_CONFIG.collections.sections);
  
  // 获取最新文档
  const latestDoc = await documentsCollection.findOne(
    {},
    { sort: { created_at: -1 } }
  );

  if (!latestDoc) return [];

  // 获取该文档的所有段落
  const sections = await sectionsCollection.find({
    document_id: latestDoc._id
  }).sort({ created_at: 1 }).toArray();

  return sections.map(section => ({
    ...section,
    id: section._id?.toString(),
    document_id: section.document_id.toString()
  }));
}

export async function getAllDocuments(): Promise<Document[]> {
  const db = await mongoManager.connect();
  const collection = mongoManager.getCollection<Document>(MONGODB_CONFIG.collections.documents);
  
  const documents = await collection.find({})
    .sort({ created_at: -1 })
    .toArray();

  return documents.map(doc => ({
    ...doc,
    id: doc._id?.toString(),
    created_at: doc.created_at.toISOString()
  }));
}

export async function deleteDocument(id: string): Promise<void> {
  const db = await mongoManager.connect();
  const documentsCollection = mongoManager.getCollection<Document>(MONGODB_CONFIG.collections.documents);
  const sectionsCollection = mongoManager.getCollection<Section>(MONGODB_CONFIG.collections.sections);
  const imagesCollection = mongoManager.getCollection<GeneratedImage>(MONGODB_CONFIG.collections.images);
  
  const objectId = new ObjectId(id);
  
  // 使用事务确保数据一致性
  const session = mongoManager.client?.startSession();
  
  try {
    await session?.withTransaction(async () => {
      // 删除相关图片
      await imagesCollection.deleteMany({ document_id: objectId });
      // 删除相关段落
      await sectionsCollection.deleteMany({ document_id: objectId });
      // 删除文档
      await documentsCollection.deleteOne({ _id: objectId });
    });
  } finally {
    await session?.endSession();
  }
}

// 新增：图片相关操作
export async function saveGeneratedImage(
  prompt: string, 
  imageUrl: string, 
  options: {
    documentId?: string;
    sectionId?: string;
    revisedPrompt?: string;
  } = {}
): Promise<string> {
  const db = await mongoManager.connect();
  const collection = mongoManager.getCollection<GeneratedImage>(MONGODB_CONFIG.collections.images);
  
  const image: Omit<GeneratedImage, '_id'> = {
    prompt,
    image_url: imageUrl,
    revised_prompt: options.revisedPrompt,
    document_id: options.documentId ? new ObjectId(options.documentId) : undefined,
    section_id: options.sectionId ? new ObjectId(options.sectionId) : undefined,
    created_at: new Date()
  };

  const result = await collection.insertOne(image);
  return result.insertedId.toString();
}

export async function getImagesByDocument(documentId: string): Promise<GeneratedImage[]> {
  const db = await mongoManager.connect();
  const collection = mongoManager.getCollection<GeneratedImage>(MONGODB_CONFIG.collections.images);
  
  const images = await collection.find({
    document_id: new ObjectId(documentId)
  }).sort({ created_at: -1 }).toArray();

  return images.map(image => ({
    ...image,
    id: image._id?.toString(),
    document_id: image.document_id?.toString(),
    section_id: image.section_id?.toString()
  }));
}

// 数据库状态检查
export async function getDbStatus(): Promise<{ initialized: boolean; error: any }> {
  try {
    await mongoManager.connect();
    return { initialized: true, error: null };
  } catch (error) {
    return { initialized: false, error };
  }
}

// 优雅关闭数据库连接
export async function closeConnection(): Promise<void> {
  await mongoManager.disconnect();
}

// 进程退出时自动关闭连接
if (typeof process !== 'undefined') {
  process.on('SIGINT', closeConnection);
  process.on('SIGTERM', closeConnection);
}
