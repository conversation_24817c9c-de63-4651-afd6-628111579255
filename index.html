<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>智能文案改写平台</title>
    
    <!-- Preload critical resources -->
    <link rel="preload" href="/assets/react-vendor-BXYUVHpj.js" as="script" crossorigin>
    <link rel="preload" href="/assets/icons-BPgN9bHE.js" as="script" crossorigin>
    
    <!-- Prefetch resources that might be needed -->
    <link rel="prefetch" href="/assets/db-EI3rwNIc.js" as="script" crossorigin>
    
    <!-- Performance optimizations -->
    <meta name="description" content="智能文案改写平台 - 高效的文本改写工具">
    <meta name="theme-color" content="#4F46E5">
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
