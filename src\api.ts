import { AI_CONFIG, IMAGE_GENERATION_CONFIG } from './config';

interface AIResponse {
  text: string;
}

interface ImageGenerationResponse {
  imageUrl: string;
  revisedPrompt?: string;
}

// AI 文本改写服务
export async function rewriteText(content: string, prompt: string): Promise<string> {
  // 首先尝试主要 API (DeepSeek)
  if (AI_CONFIG.primary.enabled) {
    try {
      return await callDeepSeekAPI(content, prompt);
    } catch (error) {
      console.warn('Primary AI API failed, trying fallback:', error);
    }
  }

  // 如果主要 API 失败，尝试备用 API (Ollama)
  if (AI_CONFIG.fallback.enabled) {
    try {
      return await callOllamaAPI(content, prompt);
    } catch (error) {
      console.error('Fallback AI API also failed:', error);
      throw new Error('所有 AI 服务都不可用，请检查网络连接或 API 配置');
    }
  }

  throw new Error('没有可用的 AI 服务');
}

// DeepSeek API 调用
async function callDeepSeekAPI(content: string, prompt: string): Promise<string> {
  const response = await fetch(`${AI_CONFIG.primary.baseUrl}/v1/chat/completions`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${AI_CONFIG.primary.apiKey}`,
    },
    body: JSON.stringify({
      model: AI_CONFIG.primary.model,
      messages: [
        {
          role: 'system',
          content: `你是一个专业的文案改写助手。${prompt}`
        },
        {
          role: 'user',
          content: content
        }
      ],
      temperature: 0.7,
      max_tokens: 2000
    }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(`DeepSeek API 请求失败: ${response.status} ${response.statusText} - ${errorData.error?.message || '未知错误'}`);
  }

  const data = await response.json();
  if (!data.choices || !data.choices[0] || !data.choices[0].message) {
    throw new Error('DeepSeek API 返回格式错误');
  }

  return data.choices[0].message.content;
}

// Ollama API 调用 (本地备用)
async function callOllamaAPI(content: string, prompt: string): Promise<string> {
  const response = await fetch(`${AI_CONFIG.fallback.baseUrl}/api/generate`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: AI_CONFIG.fallback.model,
      prompt: `你是一个专业的文案改写助手。${prompt}\n\n请改写以下内容：\n${content}`,
      stream: false,
      options: {
        temperature: 0.7,
        num_predict: 2000
      }
    }),
  });

  if (!response.ok) {
    throw new Error(`Ollama API 请求失败: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();
  if (!data.response) {
    throw new Error('Ollama API 返回格式错误');
  }

  return data.response;
}

// 文生图服务 - 腾讯云 AI (使用 OpenAI SDK 兼容接口)
export async function generateImage(prompt: string, options: {
  size?: '256x256' | '512x512' | '1024x1024';
  quality?: 'standard' | 'hd';
  style?: 'vivid' | 'natural';
} = {}): Promise<ImageGenerationResponse> {
  if (!IMAGE_GENERATION_CONFIG.enabled) {
    throw new Error('文生图服务未启用');
  }

  try {
    const response = await fetch(`${IMAGE_GENERATION_CONFIG.baseUrl}/v1/images/generations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${IMAGE_GENERATION_CONFIG.apiKey}`,
      },
      body: JSON.stringify({
        model: IMAGE_GENERATION_CONFIG.model,
        prompt: prompt,
        n: 1,
        size: options.size || '1024x1024',
        quality: options.quality || 'standard',
        style: options.style || 'vivid',
        response_format: 'url'
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`文生图 API 请求失败: ${response.status} ${response.statusText} - ${errorData.error?.message || '未知错误'}`);
    }

    const data = await response.json();
    if (!data.data || !data.data[0] || !data.data[0].url) {
      throw new Error('文生图 API 返回格式错误');
    }

    return {
      imageUrl: data.data[0].url,
      revisedPrompt: data.data[0].revised_prompt
    };
  } catch (error) {
    console.error('Error calling Image Generation API:', error);
    throw error;
  }
}

// 检查 AI 服务状态
export async function checkAIServiceStatus(): Promise<{
  primary: boolean;
  fallback: boolean;
  imageGeneration: boolean;
}> {
  const status = {
    primary: false,
    fallback: false,
    imageGeneration: false
  };

  // 检查 DeepSeek API
  if (AI_CONFIG.primary.enabled) {
    try {
      const response = await fetch(`${AI_CONFIG.primary.baseUrl}/v1/models`, {
        headers: {
          'Authorization': `Bearer ${AI_CONFIG.primary.apiKey}`,
        },
      });
      status.primary = response.ok;
    } catch (error) {
      console.warn('DeepSeek API 不可用:', error);
    }
  }

  // 检查 Ollama API
  if (AI_CONFIG.fallback.enabled) {
    try {
      const response = await fetch(`${AI_CONFIG.fallback.baseUrl}/api/tags`);
      status.fallback = response.ok;
    } catch (error) {
      console.warn('Ollama API 不可用:', error);
    }
  }

  // 检查文生图 API
  if (IMAGE_GENERATION_CONFIG.enabled) {
    try {
      // 简单的健康检查，不实际生成图片
      status.imageGeneration = true; // 假设可用，实际使用时会验证
    } catch (error) {
      console.warn('文生图 API 不可用:', error);
    }
  }

  return status;
}