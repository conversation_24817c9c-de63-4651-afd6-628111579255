import React from 'react';
import { RefreshCw, Download } from 'lucide-react';

interface SectionState {
  id: number;
  content: string;
  rewritten?: string;
}

interface SectionListProps {
  sections: SectionState[];
  selectedSections: number[];
  loading: boolean;
  onSectionSelect: (sectionId: number, selected: boolean) => void;
  onRewrite: (sectionId: number) => void;
  onBatchRewrite: () => void;
  onDownload: () => void;
}

export const SectionList: React.FC<SectionListProps> = ({
  sections,
  selectedSections,
  loading,
  onSectionSelect,
  onRewrite,
  onBatchRewrite,
  onDownload,
}) => {
  if (sections.length === 0) {
    return null;
  }

  return (
    <>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold text-gray-700">文档段落</h2>
        {selectedSections.length > 0 && (
          <button
            onClick={onBatchRewrite}
            disabled={loading}
            className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors flex items-center gap-2 disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            批量改写
          </button>
        )}
      </div>

      <div className="space-y-4">
        {sections.map((section) => (
          <div key={section.id} className="border rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <input
                type="checkbox"
                checked={selectedSections.includes(section.id)}
                onChange={(e) => onSectionSelect(section.id, e.target.checked)}
                className="w-4 h-4 text-indigo-600"
              />
              <span className="text-sm font-medium text-gray-600">段落 {section.id}</span>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-gray-50 rounded p-3">
                <div className="text-sm text-gray-500 mb-2">原文</div>
                <p className="text-gray-700">{section.content}</p>
              </div>
              
              <div className="bg-gray-50 rounded p-3">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm text-gray-500">改写后</span>
                  <button
                    onClick={() => onRewrite(section.id)}
                    disabled={loading}
                    className="text-indigo-600 hover:text-indigo-700 text-sm flex items-center gap-1"
                  >
                    <RefreshCw className={`w-3 h-3 ${loading ? 'animate-spin' : ''}`} />
                    编写提示词
                  </button>
                </div>
                <p className="text-gray-700">
                  {section.rewritten || '点击"编写提示词"开始改写'}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-8 flex justify-center">
        <button
          onClick={onDownload}
          className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
        >
          <Download className="w-5 h-5" />
          导出文档
        </button>
      </div>
    </>
  );
};