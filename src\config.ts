// AI API 配置
export const AI_CONFIG = {
  // 主要 AI API - DeepSeek
  primary: {
    name: 'DeepSeek',
    apiKey: '***********************************',
    baseUrl: 'https://api.deepseek.com',
    model: 'deepseek-reasoner',
    enabled: true
  },

  // 备用 AI API - 本地 Ollama
  fallback: {
    name: '<PERSON><PERSON><PERSON>',
    apiKey: '', // Ollama 通常不需要 API Key
    baseUrl: 'http://localhost:11434',
    model: 'llama3.1:8b', // 可根据本地模型调整
    enabled: true
  }
};

// 文生图 API 配置 - 腾讯云 AI
export const IMAGE_GENERATION_CONFIG = {
  name: 'Tencent Cloud AI',
  apiKey: 'sk-tsfTXZUNgkePyo5SBWBaLI0ayxA6USvnkoVxZTSgGrQpxaJ6',
  baseUrl: 'https://api.hunyuan.cloud.tencent.com', // 腾讯云混元API地址
  model: 'hunyuan-standard', // 腾讯云混元模型
  enabled: true
};

// MongoDB 数据库配置
export const MONGODB_CONFIG = {
  // 可以通过环境变量配置，默认本地MongoDB
  connectionString: process.env.MONGODB_URI || 'mongodb://localhost:27017/text-rewriter',
  dbName: 'text-rewriter',
  collections: {
    documents: 'documents',
    sections: 'sections',
    images: 'images' // 新增图片集合
  },
  options: {
    maxPoolSize: 10,
    serverSelectionTimeoutMS: 5000,
    socketTimeoutMS: 45000,
  }
};

// 兼容性导出（保持向后兼容）
export const DEEPSEEK_API_KEY = AI_CONFIG.primary.apiKey;
export const DEEPSEEK_MODEL = AI_CONFIG.primary.model;