import React from 'react';
import { X, RefreshCw } from 'lucide-react';

interface PromptModalProps {
  isOpen: boolean;
  customPrompt: string;
  loading: boolean;
  onClose: () => void;
  onPromptChange: (prompt: string) => void;
  onConfirm: () => void;
}

export const PromptModal: React.FC<PromptModalProps> = ({
  isOpen,
  customPrompt,
  loading,
  onClose,
  onPromptChange,
  onConfirm,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl shadow-xl max-w-lg w-full p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-semibold text-gray-800">改写提示词设置</h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
            disabled={loading}
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            自定义提示词
          </label>
          <textarea
            value={customPrompt}
            onChange={(e) => onPromptChange(e.target.value)}
            className="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            placeholder="输入自定义提示词..."
            disabled={loading}
          />
        </div>

        <div className="flex justify-end gap-3">
          <button
            onClick={onClose}
            disabled={loading}
            className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50"
          >
            取消
          </button>
          <button
            onClick={onConfirm}
            disabled={loading}
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 flex items-center gap-2"
          >
            {loading && <RefreshCw className="w-4 h-4 animate-spin" />}
            确认改写
          </button>
        </div>
      </div>
    </div>
  );
};