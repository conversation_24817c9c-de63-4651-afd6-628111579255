import React from 'react';
import { Upload } from 'lucide-react';

interface FileUploadProps {
  onFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  loading: boolean;
}

export const FileUpload: React.FC<FileUploadProps> = ({ onFileUpload, loading }) => {
  return (
    <>
      <div className="flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg p-8 mb-6">
        <label className="flex flex-col items-center cursor-pointer">
          <Upload className="w-12 h-12 text-gray-400 mb-4" />
          <div className="text-center">
            <span className="text-gray-600 block mb-2">上传文档</span>
            <span className="text-xs text-gray-400 block">支持 Word、WPS、Excel、PPT 等格式</span>
          </div>
          <input
            type="file"
            className="hidden"
            accept=".doc,.docx,.xls,.xlsx,.ppt,.pptx,.wps,.et,.dps"
            onChange={onFileUpload}
            disabled={loading}
          />
        </label>
      </div>
      <div className="text-xs text-gray-500 text-center">
        <p>支持的文件格式：</p>
        <p>Word/WPS: .doc, .docx, .wps</p>
        <p>Excel/ET: .xls, .xlsx, .et</p>
        <p>PowerPoint/DPS: .ppt, .pptx, .dps</p>
      </div>
    </>
  );
};