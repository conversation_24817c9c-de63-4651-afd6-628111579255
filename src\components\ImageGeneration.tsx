import React, { useState } from 'react';
import { Image, Loader2, Download, X, Wand2 } from 'lucide-react';
import { generateImage } from '../api';
import { saveGeneratedImage, supportsImageGeneration } from '../database';

interface ImageGenerationProps {
  documentId?: string;
  sectionId?: string;
  initialPrompt?: string;
  onClose?: () => void;
}

interface GeneratedImageData {
  id?: string;
  imageUrl: string;
  prompt: string;
  revisedPrompt?: string;
  created_at?: Date;
}

export default function ImageGeneration({ 
  documentId, 
  sectionId, 
  initialPrompt = '', 
  onClose 
}: ImageGenerationProps) {
  const [prompt, setPrompt] = useState(initialPrompt);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImages, setGeneratedImages] = useState<GeneratedImageData[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [imageSize, setImageSize] = useState<'256x256' | '512x512' | '1024x1024'>('1024x1024');
  const [imageStyle, setImageStyle] = useState<'vivid' | 'natural'>('vivid');
  const [imageQuality, setImageQuality] = useState<'standard' | 'hd'>('standard');

  // 检查是否支持图片功能
  if (!supportsImageGeneration()) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-center">
          <Image className="h-5 w-5 text-yellow-600 mr-2" />
          <p className="text-yellow-800">
            文生图功能需要 MongoDB 数据库支持。当前使用的是浏览器端数据库，不支持图片存储。
          </p>
        </div>
      </div>
    );
  }

  const handleGenerateImage = async () => {
    if (!prompt.trim()) {
      setError('请输入图片描述');
      return;
    }

    setIsGenerating(true);
    setError(null);

    try {
      const result = await generateImage(prompt, {
        size: imageSize,
        quality: imageQuality,
        style: imageStyle
      });

      const newImage: GeneratedImageData = {
        imageUrl: result.imageUrl,
        prompt: prompt,
        revisedPrompt: result.revisedPrompt,
        created_at: new Date()
      };

      // 如果有文档ID，保存到数据库
      if (documentId) {
        const imageId = await saveGeneratedImage(prompt, result.imageUrl, {
          documentId,
          sectionId,
          revisedPrompt: result.revisedPrompt
        });
        newImage.id = imageId;
      }

      setGeneratedImages(prev => [newImage, ...prev]);
    } catch (error) {
      console.error('生成图片失败:', error);
      setError(error instanceof Error ? error.message : '生成图片失败，请重试');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDownloadImage = async (imageUrl: string, prompt: string) => {
    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `generated-image-${Date.now()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('下载图片失败:', error);
      setError('下载图片失败');
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-4xl mx-auto">
      {/* 头部 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Wand2 className="h-6 w-6 text-purple-600 mr-2" />
          <h2 className="text-xl font-semibold text-gray-900">AI 文生图</h2>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        )}
      </div>

      {/* 生成配置 */}
      <div className="space-y-4 mb-6">
        {/* 提示词输入 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            图片描述 *
          </label>
          <textarea
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            placeholder="请描述您想要生成的图片内容..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
            rows={3}
          />
        </div>

        {/* 生成选项 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              图片尺寸
            </label>
            <select
              value={imageSize}
              onChange={(e) => setImageSize(e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="256x256">256×256</option>
              <option value="512x512">512×512</option>
              <option value="1024x1024">1024×1024</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              图片风格
            </label>
            <select
              value={imageStyle}
              onChange={(e) => setImageStyle(e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="vivid">生动</option>
              <option value="natural">自然</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              图片质量
            </label>
            <select
              value={imageQuality}
              onChange={(e) => setImageQuality(e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="standard">标准</option>
              <option value="hd">高清</option>
            </select>
          </div>
        </div>

        {/* 生成按钮 */}
        <button
          onClick={handleGenerateImage}
          disabled={isGenerating || !prompt.trim()}
          className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
        >
          {isGenerating ? (
            <>
              <Loader2 className="animate-spin h-4 w-4 mr-2" />
              生成中...
            </>
          ) : (
            <>
              <Wand2 className="h-4 w-4 mr-2" />
              生成图片
            </>
          )}
        </button>
      </div>

      {/* 错误信息 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-6">
          <p className="text-red-800 text-sm">{error}</p>
        </div>
      )}

      {/* 生成的图片 */}
      {generatedImages.length > 0 && (
        <div className="space-y-6">
          <h3 className="text-lg font-medium text-gray-900">生成的图片</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {generatedImages.map((image, index) => (
              <div key={index} className="border border-gray-200 rounded-lg overflow-hidden">
                <div className="aspect-square bg-gray-100">
                  <img
                    src={image.imageUrl}
                    alt={image.prompt}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk3YTNiNCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWbvueJh+WKoOi9veWksei0pTwvdGV4dD48L3N2Zz4=';
                    }}
                  />
                </div>
                <div className="p-4">
                  <p className="text-sm text-gray-600 mb-2">
                    <strong>提示词:</strong> {image.prompt}
                  </p>
                  {image.revisedPrompt && (
                    <p className="text-sm text-gray-500 mb-3">
                      <strong>优化后:</strong> {image.revisedPrompt}
                    </p>
                  )}
                  <button
                    onClick={() => handleDownloadImage(image.imageUrl, image.prompt)}
                    className="flex items-center text-purple-600 hover:text-purple-700 transition-colors"
                  >
                    <Download className="h-4 w-4 mr-1" />
                    下载图片
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
