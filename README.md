# 智能文案改写平台

一个基于 React + TypeScript 构建的智能文案改写工具，支持多种文档格式上传，使用 DeepSeek AI 进行文本改写，并提供本地数据存储功能。

## ✨ 功能特性

### 📄 文档处理
- **多格式支持**: 支持 Word (.doc, .docx, .wps)、Excel (.xls, .xlsx, .et)、PowerPoint (.ppt, .pptx, .dps) 等格式
- **智能分段**: 自动将文档内容分割为合理的段落，便于逐段改写
- **内容预处理**: 自动清理特殊字符，规范化文本格式

### 🤖 AI 改写
- **多AI支持**: 主要使用 DeepSeek AI，备用本地 Ollama 服务
- **智能切换**: 主要API失败时自动切换到备用API
- **自定义提示词**: 支持用户自定义改写风格和要求
- **批量处理**: 支持选择多个段落进行批量改写
- **实时预览**: 原文与改写结果对比显示

### 🎨 AI 文生图
- **腾讯云集成**: 使用腾讯云混元AI进行图片生成
- **OpenAI兼容**: 使用OpenAI SDK方式接入，易于扩展
- **多种配置**: 支持不同尺寸、风格和质量设置
- **图片管理**: 自动保存生成的图片到数据库
- **图片画廊**: 查看和管理文档相关的所有图片

### 💾 数据管理
- **双数据库支持**: 支持 MongoDB 和浏览器端 SQL.js
- **智能适配**: 根据环境自动选择合适的数据库
- **文档管理**: 保存和管理多个文档项目
- **历史记录**: 保留改写历史，支持随时查看和编辑
- **图片存储**: MongoDB 环境下支持图片数据存储
- **数据迁移**: 提供从 SQL.js 到 MongoDB 的迁移工具

### 🎨 用户界面
- **现代化设计**: 基于 Tailwind CSS 的美观界面
- **响应式布局**: 适配桌面和移动设备
- **直观操作**: 拖拽上传、一键改写、批量操作
- **状态反馈**: 实时显示处理进度和状态

## 🚀 快速开始

### 环境要求
- Node.js 16.0 或更高版本
- npm 或 yarn 包管理器

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd text-rewriter
```

2. **安装依赖**
```bash
npm install
```

3. **配置 API 密钥**
编辑 `src/config.ts` 文件，设置你的 DeepSeek API 密钥：
```typescript
export const DEEPSEEK_API_KEY = 'your-api-key-here';
export const DEEPSEEK_MODEL = 'deepseek-reasoner';
```

4. **启动开发服务器**
```bash
npm run dev
```

5. **访问应用**
打开浏览器访问 `http://localhost:3000`

## 📖 使用指南

### 上传文档
1. 点击上传区域或拖拽文件到上传区域
2. 支持的文件格式：
   - Word 文档: `.doc`, `.docx`, `.wps`
   - Excel 表格: `.xls`, `.xlsx`, `.et`
   - PowerPoint 演示文稿: `.ppt`, `.pptx`, `.dps`

### 改写文本
1. **单段改写**: 点击段落右侧的"编写提示词"按钮
2. **批量改写**: 选择多个段落，点击"批量改写"按钮
3. **自定义提示词**: 在弹出的对话框中输入改写要求
4. **查看结果**: 改写完成后在右侧查看结果

### 管理文档
1. **查看历史**: 在文档列表中查看已保存的文档
2. **加载文档**: 点击文档名称重新加载
3. **删除文档**: 点击删除按钮移除不需要的文档
4. **导出结果**: 点击"导出文档"下载改写后的文本

## 🏗️ 项目架构

### 技术栈
- **前端框架**: React 18 + TypeScript
- **构建工具**: Vite
- **样式框架**: Tailwind CSS
- **图标库**: Lucide React
- **数据库**: MongoDB + SQL.js (双数据库支持)
- **文件处理**: mammoth.js (Word), xlsx (Excel)
- **AI 服务**: DeepSeek API + Ollama (备用)
- **文生图服务**: 腾讯云混元AI

### 目录结构
```
src/
├── components/          # React 组件
│   ├── DocumentList.tsx    # 文档列表组件
│   ├── FileUpload.tsx      # 文件上传组件
│   ├── SectionList.tsx     # 段落列表组件
│   ├── PromptModal.tsx     # 提示词模态框
│   ├── DeleteConfirmModal.tsx # 删除确认模态框
│   ├── ImageGeneration.tsx # 图片生成组件
│   ├── ImageGallery.tsx    # 图片画廊组件
│   └── index.ts            # 组件导出
├── utils/               # 工具函数
│   └── fileProcessing.ts   # 文件处理工具
├── App.tsx             # 主应用组件
├── api.ts              # API 接口（多AI支持）
├── config.ts           # 配置文件（AI和数据库配置）
├── db.ts               # SQL.js 数据库操作
├── mongodb.ts          # MongoDB 数据库操作
├── database.ts         # 数据库适配器
├── main.tsx            # 应用入口
└── index.css           # 全局样式
```

### 核心模块

#### 文件处理 (`utils/fileProcessing.ts`)
- 支持多种文档格式解析
- 智能文本分段算法
- 懒加载优化，按需加载处理库

#### 数据库操作
- **SQL.js (`db.ts`)**: 浏览器端 SQLite 数据库
- **MongoDB (`mongodb.ts`)**: 服务端 MongoDB 数据库
- **适配器 (`database.ts`)**: 统一数据库接口，自动选择合适的数据库

#### API 集成 (`api.ts`)
- **多AI支持**: DeepSeek + Ollama 双重保障
- **文生图集成**: 腾讯云混元AI图片生成
- **智能切换**: 主要API失败时自动切换备用
- **错误处理**: 完善的错误处理和重试机制

## ⚡ 性能优化

### 代码分割
- **懒加载**: 文件处理库按需加载
- **代码分块**: Vendor 库独立打包
- **动态导入**: 减少初始包大小

### 打包优化
- **初始加载**: 从 1,018KB 优化到 158KB (85% 减少)
- **按需加载**: 文件处理功能仅在使用时加载
- **缓存策略**: 长期缓存静态资源

详细的性能分析报告请查看 [PERFORMANCE_ANALYSIS.md](./PERFORMANCE_ANALYSIS.md)

## 🔧 开发指南

### 可用脚本
```bash
# 开发模式
npm run dev

# 构建生产版本
npm run build

# 预览生产构建
npm run preview
```

### 代码规范
- 使用 TypeScript 进行类型检查
- 遵循 ESLint 代码规范
- 组件化开发，单一职责原则
- 文件大小控制在 200 行以内

### 添加新功能
1. 在 `components/` 目录下创建新组件
2. 在 `utils/` 目录下添加工具函数
3. 更新 `App.tsx` 集成新功能
4. 添加相应的类型定义

## 🔒 安全考虑

### API 密钥管理
- 不要将 API 密钥提交到版本控制
- 生产环境使用环境变量
- 定期轮换 API 密钥

### 数据安全
- 本地数据存储，不上传到服务器
- 支持数据导出和备份
- 用户可随时清除本地数据

## 🐛 故障排除

### 常见问题

**Q: 上传文件后没有反应？**
A: 检查文件格式是否支持，确保文件没有损坏

**Q: 改写功能不工作？**
A: 检查 API 密钥是否正确配置，网络连接是否正常

**Q: 数据库连接失败？**
A: 清除浏览器缓存，刷新页面重试

**Q: 文件处理失败？**
A: 确保文件格式正确，文件大小不超过限制

### 调试模式
开发环境下会显示详细的错误信息和调试日志

## 🤝 贡献指南

### 提交代码
1. Fork 项目仓库
2. 创建功能分支
3. 提交代码更改
4. 创建 Pull Request

### 报告问题
- 使用 GitHub Issues 报告 Bug
- 提供详细的复现步骤
- 包含错误截图和日志

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [DeepSeek](https://www.deepseek.com/) - 提供 AI 改写服务
- [SQL.js](https://sql.js.org/) - 浏览器端 SQLite 数据库
- [mammoth.js](https://github.com/mwilliamson/mammoth.js) - Word 文档解析
- [SheetJS](https://sheetjs.com/) - Excel 文件处理
- [Lucide](https://lucide.dev/) - 图标库
- [Tailwind CSS](https://tailwindcss.com/) - CSS 框架

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 项目 Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 邮箱: <EMAIL>

---

**智能文案改写平台** - 让文案改写更简单、更高效！