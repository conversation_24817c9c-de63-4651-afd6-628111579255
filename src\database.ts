// 数据库适配器 - 支持 SQL.js 和 MongoDB 之间的切换
import { MONGODB_CONFIG } from './config';

// 统一的数据接口
export interface Document {
  id: string | number;
  title: string;
  created_at: string | Date;
}

export interface Section {
  id: string | number;
  document_id: string | number;
  content: string;
  rewritten?: string;
}

export interface GeneratedImage {
  id: string;
  document_id?: string;
  section_id?: string;
  prompt: string;
  image_url: string;
  revised_prompt?: string;
  created_at: Date;
}

// 数据库类型
type DatabaseType = 'mongodb' | 'sqljs';

// 检测数据库类型
function detectDatabaseType(): DatabaseType {
  // 如果在浏览器环境且没有配置 MongoDB，使用 SQL.js
  if (typeof window !== 'undefined' && !MONGODB_CONFIG.connectionString.includes('mongodb://')) {
    return 'sqljs';
  }
  // 否则使用 MongoDB
  return 'mongodb';
}

const DB_TYPE: DatabaseType = detectDatabaseType();

// 动态导入数据库模块
async function getDbModule() {
  if (DB_TYPE === 'mongodb') {
    return await import('./mongodb');
  } else {
    return await import('./db');
  }
}

// 统一的数据库操作接口
export async function createDocument(title: string): Promise<string | number> {
  const dbModule = await getDbModule();
  return await dbModule.createDocument(title);
}

export async function saveSections(documentId: string | number, sections: { content: string }[]): Promise<Section[]> {
  const dbModule = await getDbModule();
  const result = await dbModule.saveSections(documentId.toString(), sections);
  
  // 统一返回格式
  return result.map(section => ({
    id: section.id || section._id?.toString() || section.id,
    document_id: section.document_id,
    content: section.content,
    rewritten: section.rewritten
  }));
}

export async function updateSection(id: string | number, rewritten: string): Promise<void> {
  const dbModule = await getDbModule();
  return await dbModule.updateSection(id.toString(), rewritten);
}

export async function getSections(documentId: string | number): Promise<Section[]> {
  const dbModule = await getDbModule();
  const result = await dbModule.getSections(documentId.toString());
  
  // 统一返回格式
  return result.map(section => ({
    id: section.id || section._id?.toString() || section.id,
    document_id: section.document_id,
    content: section.content,
    rewritten: section.rewritten
  }));
}

export async function getLatestDocument(): Promise<Section[]> {
  const dbModule = await getDbModule();
  const result = await dbModule.getLatestDocument();
  
  // 统一返回格式
  return result.map(section => ({
    id: section.id || section._id?.toString() || section.id,
    document_id: section.document_id,
    content: section.content,
    rewritten: section.rewritten
  }));
}

export async function getAllDocuments(): Promise<Document[]> {
  const dbModule = await getDbModule();
  const result = await dbModule.getAllDocuments();
  
  // 统一返回格式
  return result.map(doc => ({
    id: doc.id || doc._id?.toString() || doc.id,
    title: doc.title,
    created_at: doc.created_at
  }));
}

export async function deleteDocument(id: string | number): Promise<void> {
  const dbModule = await getDbModule();
  return await dbModule.deleteDocument(id.toString());
}

export async function getDbStatus(): Promise<{ initialized: boolean; error: any }> {
  const dbModule = await getDbModule();
  return await dbModule.getDbStatus();
}

// 图片相关操作（仅 MongoDB 支持）
export async function saveGeneratedImage(
  prompt: string, 
  imageUrl: string, 
  options: {
    documentId?: string;
    sectionId?: string;
    revisedPrompt?: string;
  } = {}
): Promise<string> {
  if (DB_TYPE === 'sqljs') {
    throw new Error('SQL.js 数据库不支持图片存储功能，请使用 MongoDB');
  }
  
  const dbModule = await getDbModule();
  if ('saveGeneratedImage' in dbModule) {
    return await dbModule.saveGeneratedImage(prompt, imageUrl, options);
  }
  
  throw new Error('当前数据库不支持图片存储功能');
}

export async function getImagesByDocument(documentId: string): Promise<GeneratedImage[]> {
  if (DB_TYPE === 'sqljs') {
    return []; // SQL.js 不支持图片，返回空数组
  }
  
  const dbModule = await getDbModule();
  if ('getImagesByDocument' in dbModule) {
    const result = await dbModule.getImagesByDocument(documentId);
    return result.map(image => ({
      id: image.id || image._id?.toString() || '',
      document_id: image.document_id?.toString(),
      section_id: image.section_id?.toString(),
      prompt: image.prompt,
      image_url: image.image_url,
      revised_prompt: image.revised_prompt,
      created_at: image.created_at
    }));
  }
  
  return [];
}

// 数据迁移工具（从 SQL.js 迁移到 MongoDB）
export async function migrateFromSqlJsToMongoDB(): Promise<void> {
  if (DB_TYPE !== 'mongodb') {
    throw new Error('当前配置不是 MongoDB，无法执行迁移');
  }

  try {
    // 导入 SQL.js 模块
    const sqlJsModule = await import('./db');
    const mongoModule = await import('./mongodb');

    console.log('开始数据迁移...');

    // 获取 SQL.js 中的所有文档
    const sqlDocuments = await sqlJsModule.getAllDocuments();
    
    for (const sqlDoc of sqlDocuments) {
      // 在 MongoDB 中创建文档
      const mongoDocId = await mongoModule.createDocument(sqlDoc.title);
      
      // 获取该文档的所有段落
      const sqlSections = await sqlJsModule.getSections(sqlDoc.id);
      
      if (sqlSections.length > 0) {
        // 迁移段落数据
        const sectionsToMigrate = sqlSections.map(section => ({
          content: section.content
        }));
        
        const mongoSections = await mongoModule.saveSections(mongoDocId, sectionsToMigrate);
        
        // 更新已改写的内容
        for (let i = 0; i < sqlSections.length; i++) {
          if (sqlSections[i].rewritten) {
            await mongoModule.updateSection(mongoSections[i].id!, sqlSections[i].rewritten!);
          }
        }
      }
    }

    console.log(`成功迁移 ${sqlDocuments.length} 个文档到 MongoDB`);
  } catch (error) {
    console.error('数据迁移失败:', error);
    throw error;
  }
}

// 获取当前数据库类型
export function getCurrentDatabaseType(): DatabaseType {
  return DB_TYPE;
}

// 检查是否支持图片功能
export function supportsImageGeneration(): boolean {
  return DB_TYPE === 'mongodb';
}
