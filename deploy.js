const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = 3000;
const DIST_DIR = path.join(__dirname, 'dist');

// MIME types
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'text/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.wav': 'audio/wav',
  '.mp4': 'video/mp4',
  '.woff': 'application/font-woff',
  '.ttf': 'application/font-ttf',
  '.eot': 'application/vnd.ms-fontobject',
  '.otf': 'application/font-otf',
  '.wasm': 'application/wasm'
};

const server = http.createServer((req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const parsedUrl = url.parse(req.url);
  let pathname = parsedUrl.pathname;

  // Default to index.html for SPA routing
  if (pathname === '/') {
    pathname = '/index.html';
  }

  const filePath = path.join(DIST_DIR, pathname);
  const ext = path.parse(filePath).ext;
  const mimeType = mimeTypes[ext] || 'text/plain';

  fs.readFile(filePath, (err, data) => {
    if (err) {
      // If file not found, serve index.html for SPA routing
      if (err.code === 'ENOENT') {
        fs.readFile(path.join(DIST_DIR, 'index.html'), (err, data) => {
          if (err) {
            res.writeHead(500);
            res.end('Internal Server Error');
          } else {
            res.writeHead(200, { 'Content-Type': 'text/html' });
            res.end(data);
          }
        });
      } else {
        res.writeHead(500);
        res.end('Internal Server Error');
      }
    } else {
      res.writeHead(200, { 'Content-Type': mimeType });
      res.end(data);
    }
  });
});

server.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 智能文本重写平台已启动！`);
  console.log(`📍 本地访问地址: http://localhost:${PORT}`);
  console.log(`🌐 网络访问地址: http://0.0.0.0:${PORT}`);
  console.log(`📁 服务目录: ${DIST_DIR}`);
  console.log(`⏰ 启动时间: ${new Date().toLocaleString()}`);
  console.log(`\n✨ 功能特性:`);
  console.log(`   • AI智能文本重写 (DeepSeek + Ollama备用)`);
  console.log(`   • 文件批量处理 (Word, Excel, TXT)`);
  console.log(`   • 图像生成功能 (腾讯云AI)`);
  console.log(`   • 本地数据库存储 (SQL.js)`);
  console.log(`\n🎯 使用说明:`);
  console.log(`   1. 在浏览器中打开上述地址`);
  console.log(`   2. 上传文件或输入文本进行重写`);
  console.log(`   3. 查看历史记录和管理数据`);
  console.log(`\n按 Ctrl+C 停止服务器`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\n🛑 正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已安全关闭');
    process.exit(0);
  });
});
