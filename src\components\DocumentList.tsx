import React from 'react';
import { ChevronRight, Trash2 } from 'lucide-react';
import { Document } from '../db';

interface DocumentListProps {
  documents: Document[];
  onLoadDocument: (documentId: number) => void;
  onDeleteDocument: (documentId: number) => void;
}

export const DocumentList: React.FC<DocumentListProps> = ({
  documents,
  onLoadDocument,
  onDeleteDocument,
}) => {
  if (documents.length === 0) {
    return null;
  }

  return (
    <div className="mb-8">
      <h2 className="text-xl font-semibold text-gray-700 mb-4">已保存的文档</h2>
      <div className="space-y-2">
        {documents.map((doc) => (
          <div
            key={doc.id}
            className="w-full px-4 py-3 rounded-lg border border-gray-200 flex items-center justify-between"
          >
            <button
              onClick={() => onLoadDocument(doc.id)}
              className="flex-1 text-left hover:bg-gray-50 flex items-center justify-between"
            >
              <span className="font-medium text-gray-700">{doc.title}</span>
              <span className="text-sm text-gray-500 ml-2">
                {new Date(doc.created_at).toLocaleString()}
              </span>
            </button>
            <div className="flex items-center gap-2">
              <button
                onClick={() => onDeleteDocument(doc.id)}
                className="p-2 text-red-600 hover:bg-red-50 rounded-full transition-colors"
              >
                <Trash2 className="w-4 h-4" />
              </button>
              <ChevronRight className="w-5 h-5 text-gray-400" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};