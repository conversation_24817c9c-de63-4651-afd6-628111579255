<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 智能文本重写平台 - 测试页面</h1>
        
        <div class="status info">
            <strong>📋 测试说明:</strong> 这是一个简单的测试页面，用于验证应用的基本功能。
        </div>

        <div class="test-section">
            <h3>🔗 资源加载测试</h3>
            <button onclick="testResourceLoading()">测试资源加载</button>
            <div id="resource-result"></div>
        </div>

        <div class="test-section">
            <h3>🎯 React应用测试</h3>
            <button onclick="testReactApp()">测试React应用</button>
            <div id="react-result"></div>
        </div>

        <div class="test-section">
            <h3>📁 文件路径测试</h3>
            <button onclick="testFilePaths()">测试文件路径</button>
            <div id="path-result"></div>
        </div>

        <div id="result"></div>
    </div>

    <script>
        function testResourceLoading() {
            const resultDiv = document.getElementById('resource-result');
            resultDiv.innerHTML = '<div class="status info">正在测试资源加载...</div>';
            
            const resources = [
                './assets/index-DJwsUP15.js',
                './assets/react-vendor-BXYUVHpj.js',
                './assets/index-BUtRi5af.css'
            ];
            
            let results = [];
            let completed = 0;
            
            resources.forEach(resource => {
                fetch(resource)
                    .then(response => {
                        results.push(`✅ ${resource}: ${response.status} ${response.statusText}`);
                    })
                    .catch(error => {
                        results.push(`❌ ${resource}: ${error.message}`);
                    })
                    .finally(() => {
                        completed++;
                        if (completed === resources.length) {
                            resultDiv.innerHTML = `<div class="status ${results.every(r => r.includes('✅')) ? 'success' : 'error'}">
                                ${results.join('<br>')}
                            </div>`;
                        }
                    });
            });
        }

        function testReactApp() {
            const resultDiv = document.getElementById('react-result');
            resultDiv.innerHTML = '<div class="status info">正在检查React应用...</div>';
            
            // 检查React根元素
            const rootElement = document.getElementById('root');
            if (rootElement) {
                resultDiv.innerHTML = '<div class="status success">✅ React根元素存在</div>';
            } else {
                resultDiv.innerHTML = '<div class="status error">❌ React根元素不存在</div>';
            }
        }

        function testFilePaths() {
            const resultDiv = document.getElementById('path-result');
            resultDiv.innerHTML = '<div class="status info">正在测试文件路径...</div>';
            
            const currentPath = window.location.href;
            const basePath = currentPath.substring(0, currentPath.lastIndexOf('/'));
            
            resultDiv.innerHTML = `<div class="status info">
                <strong>当前路径:</strong> ${currentPath}<br>
                <strong>基础路径:</strong> ${basePath}<br>
                <strong>协议:</strong> ${window.location.protocol}
            </div>`;
        }

        // 页面加载时自动运行测试
        window.onload = function() {
            document.getElementById('result').innerHTML = `
                <div class="status success">
                    <strong>✅ 测试页面加载成功!</strong><br>
                    时间: ${new Date().toLocaleString()}<br>
                    用户代理: ${navigator.userAgent}
                </div>
            `;
        };
    </script>
</body>
</html>
