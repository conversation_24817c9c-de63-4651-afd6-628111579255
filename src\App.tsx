import React, { useState, useEffect } from 'react';
import { rewriteText } from './api';
import { 
  createDocument, 
  saveSections, 
  updateSection, 
  getAllDocuments, 
  getSections, 
  getDbStatus, 
  deleteDocument, 
  Document 
} from './db';
import { 
  DocumentList, 
  FileUpload, 
  SectionList, 
  PromptModal, 
  DeleteConfirmModal 
} from './components';
import { processFile, processContent } from './utils/fileProcessing';

interface SectionState {
  id: number;
  content: string;
  rewritten?: string;
}

function App() {
  const [sections, setSections] = useState<SectionState[]>([]);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [dbStatus, setDbStatus] = useState({ initialized: false, error: null });
  const [loading, setLoading] = useState(false);
  const [selectedSections, setSelectedSections] = useState<number[]>([]);
  const [showPromptModal, setShowPromptModal] = useState(false);
  const [customPrompt, setCustomPrompt] = useState(
    '请对输入的文本进行改写，保持原意的同时使表达更加优美流畅。'
  );
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<number | null>(null);
  const [currentSectionId, setCurrentSectionId] = useState<number | null>(null);

  // Load documents on mount
  useEffect(() => {
    loadDocuments();
    const status = getDbStatus();
    setDbStatus(status);
  }, []);

  const loadDocuments = async () => {
    const docs = await getAllDocuments();
    setDocuments(docs);
  };

  const loadDocument = async (documentId: number) => {
    const docSections = await getSections(documentId);
    setSections(docSections.map(section => ({
      id: section.id,
      content: section.content,
      rewritten: section.rewritten
    })));
  };

  const handleDeleteDocument = async (id: number) => {
    setDocumentToDelete(id);
    setShowDeleteConfirm(true);
  };

  const confirmDelete = async () => {
    if (documentToDelete) {
      try {
        await deleteDocument(documentToDelete);
        await loadDocuments();
        if (sections.length > 0 && sections[0].id === documentToDelete) {
          setSections([]);
        }
      } catch (error) {
        console.error('删除失败:', error);
        alert('删除文档失败，请稍后重试');
      }
    }
    setShowDeleteConfirm(false);
    setDocumentToDelete(null);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFile(file);
    }
  };

  const handleFile = async (file: File) => {
    setLoading(true);
    try {
      // Use lazy-loaded file processing
      const content = await processFile(file);
      const paragraphs = processContent(content);

      // Save to database
      const documentId = await createDocument(file.name);
      const savedSections = await saveSections(documentId, paragraphs.map(content => ({ content })));
      
      setSections(savedSections.map(section => ({
        id: section.id,
        content: section.content,
        rewritten: section.rewritten
      })));
      
      // Refresh documents list
      await loadDocuments();

    } catch (error) {
      console.error('文件处理失败:', error);
      alert('文件处理失败，请确保文件格式正确并重试');
    } finally {
      setLoading(false);
    }
  };

  const handleRewrite = async (sectionId: number) => {
    setCurrentSectionId(sectionId);
    setShowPromptModal(true);
  };

  const handleConfirmRewrite = async () => {
    setLoading(true);
    try {
      const section = sections.find(s => s.id === currentSectionId);
      if (!section) {
        setShowPromptModal(false);
        return;
      }

      const rewritten = await rewriteText(section.content, customPrompt);
      await updateSection(currentSectionId, rewritten);
      
      setSections(sections.map(section => 
        section.id === currentSectionId 
          ? { ...section, rewritten }
          : section
      ));
      setShowPromptModal(false);
    } catch (error) {
      console.error('改写失败:', error);
      alert('改写失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleBatchRewrite = async () => {
    setLoading(true);
    try {
      const updatedSections = await Promise.all(
        sections.map(async section => {
          if (selectedSections.includes(section.id)) {
            const rewritten = await rewriteText(section.content, customPrompt);
            await updateSection(section.id, rewritten);
            return { ...section, rewritten };
          }
          return section;
        })
      );
      setSections(updatedSections);
    } catch (error) {
      console.error('批量改写失败:', error);
      alert('批量改写失败，请稍后重试');
    } finally {
      setLoading(false);
      setSelectedSections([]);
    }
  };

  const handleDownload = () => {
    const content = sections.map(section => section.rewritten || section.content).join('\n\n');
    const blob = new Blob([new TextEncoder().encode(content)], { 
      type: 'text/plain;charset=utf-8' 
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `改写后的文档.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleSectionSelect = (sectionId: number, selected: boolean) => {
    if (selected) {
      setSelectedSections([...selectedSections, sectionId]);
    } else {
      setSelectedSections(selectedSections.filter(id => id !== sectionId));
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-800 mb-8 text-center">
            智能文案改写平台
          </h1>
          
          {/* Database Status Indicator */}
          <div className={`mb-4 text-center text-sm ${dbStatus.error ? 'text-red-600' : 'text-green-600'}`}>
            <span className="inline-flex items-center gap-2">
              <span className={`w-2 h-2 rounded-full ${dbStatus.initialized ? 'bg-green-500' : 'bg-red-500'}`} />
              {dbStatus.initialized ? '数据库已连接' : '数据库未连接'}
            </span>
          </div>
          
          <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
            <DocumentList 
              documents={documents}
              onLoadDocument={loadDocument}
              onDeleteDocument={handleDeleteDocument}
            />

            <FileUpload 
              onFileUpload={handleFileUpload}
              loading={loading}
            />

            <SectionList 
              sections={sections}
              selectedSections={selectedSections}
              loading={loading}
              onSectionSelect={handleSectionSelect}
              onRewrite={handleRewrite}
              onBatchRewrite={handleBatchRewrite}
              onDownload={handleDownload}
            />
          </div>
        </div>
      </div>

      <PromptModal 
        isOpen={showPromptModal}
        customPrompt={customPrompt}
        loading={loading}
        onClose={() => setShowPromptModal(false)}
        onPromptChange={setCustomPrompt}
        onConfirm={handleConfirmRewrite}
      />

      <DeleteConfirmModal 
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={confirmDelete}
      />
    </div>
  );
}

export default App;