import React, { useState, useEffect } from 'react';
import { rewriteText, checkAIServiceStatus } from './api';
import {
  createDocument,
  saveSections,
  updateSection,
  getAllDocuments,
  getSections,
  getDbStatus,
  deleteDocument,
  Document,
  getCurrentDatabaseType,
  supportsImageGeneration
} from './database';
import {
  DocumentList,
  FileUpload,
  SectionList,
  PromptModal,
  DeleteConfirmModal,
  ImageGeneration,
  ImageGallery
} from './components';
import { processFile, processContent } from './utils/fileProcessing';
import { Image, Wand2, Database, Wifi, WifiOff } from 'lucide-react';

interface SectionState {
  id: string | number;
  content: string;
  rewritten?: string;
}

function App() {
  const [sections, setSections] = useState<SectionState[]>([]);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [dbStatus, setDbStatus] = useState({ initialized: false, error: null });
  const [loading, setLoading] = useState(false);
  const [selectedSections, setSelectedSections] = useState<(string | number)[]>([]);
  const [showPromptModal, setShowPromptModal] = useState(false);
  const [customPrompt, setCustomPrompt] = useState(
    '请对输入的文本进行改写，保持原意的同时使表达更加优美流畅。'
  );
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<string | number | null>(null);
  const [currentSectionId, setCurrentSectionId] = useState<string | number | null>(null);
  const [currentDocumentId, setCurrentDocumentId] = useState<string | number | null>(null);
  const [showImageGeneration, setShowImageGeneration] = useState(false);
  const [showImageGallery, setShowImageGallery] = useState(false);
  const [aiServiceStatus, setAiServiceStatus] = useState({
    primary: false,
    fallback: false,
    imageGeneration: false
  });
  const [databaseType, setDatabaseType] = useState<string>('');
  const [imageSupported, setImageSupported] = useState(false);

  // Load documents on mount
  useEffect(() => {
    loadDocuments();
    checkDatabaseStatus();
    checkAIServices();
    setDatabaseType(getCurrentDatabaseType());
    setImageSupported(supportsImageGeneration());
  }, []);

  const checkDatabaseStatus = async () => {
    try {
      const status = await getDbStatus();
      setDbStatus(status);
    } catch (error) {
      setDbStatus({ initialized: false, error });
    }
  };

  const checkAIServices = async () => {
    try {
      const status = await checkAIServiceStatus();
      setAiServiceStatus(status);
    } catch (error) {
      console.warn('检查AI服务状态失败:', error);
    }
  };

  const loadDocuments = async () => {
    const docs = await getAllDocuments();
    setDocuments(docs);
  };

  const loadDocument = async (documentId: string | number) => {
    const docSections = await getSections(documentId);
    setSections(docSections.map(section => ({
      id: section.id,
      content: section.content,
      rewritten: section.rewritten
    })));
    setCurrentDocumentId(documentId);
  };

  const handleDeleteDocument = async (id: string | number) => {
    setDocumentToDelete(id);
    setShowDeleteConfirm(true);
  };

  const confirmDelete = async () => {
    if (documentToDelete) {
      try {
        await deleteDocument(documentToDelete);
        await loadDocuments();
        if (sections.length > 0 && sections[0].id === documentToDelete) {
          setSections([]);
        }
      } catch (error) {
        console.error('删除失败:', error);
        alert('删除文档失败，请稍后重试');
      }
    }
    setShowDeleteConfirm(false);
    setDocumentToDelete(null);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFile(file);
    }
  };

  const handleFile = async (file: File) => {
    setLoading(true);
    try {
      // Use lazy-loaded file processing
      const content = await processFile(file);
      const paragraphs = processContent(content);

      // Save to database
      const documentId = await createDocument(file.name);
      const savedSections = await saveSections(documentId, paragraphs.map(content => ({ content })));

      setSections(savedSections.map(section => ({
        id: section.id,
        content: section.content,
        rewritten: section.rewritten
      })));

      setCurrentDocumentId(documentId);

      // Refresh documents list
      await loadDocuments();

    } catch (error) {
      console.error('文件处理失败:', error);
      alert('文件处理失败，请确保文件格式正确并重试');
    } finally {
      setLoading(false);
    }
  };

  const handleRewrite = async (sectionId: string | number) => {
    setCurrentSectionId(sectionId);
    setShowPromptModal(true);
  };

  const handleConfirmRewrite = async () => {
    setLoading(true);
    try {
      const section = sections.find(s => s.id === currentSectionId);
      if (!section) {
        setShowPromptModal(false);
        return;
      }

      const rewritten = await rewriteText(section.content, customPrompt);
      await updateSection(currentSectionId, rewritten);
      
      setSections(sections.map(section => 
        section.id === currentSectionId 
          ? { ...section, rewritten }
          : section
      ));
      setShowPromptModal(false);
    } catch (error) {
      console.error('改写失败:', error);
      alert('改写失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleBatchRewrite = async () => {
    setLoading(true);
    try {
      const updatedSections = await Promise.all(
        sections.map(async section => {
          if (selectedSections.includes(section.id)) {
            const rewritten = await rewriteText(section.content, customPrompt);
            await updateSection(section.id, rewritten);
            return { ...section, rewritten };
          }
          return section;
        })
      );
      setSections(updatedSections);
    } catch (error) {
      console.error('批量改写失败:', error);
      alert('批量改写失败，请稍后重试');
    } finally {
      setLoading(false);
      setSelectedSections([]);
    }
  };

  const handleDownload = () => {
    const content = sections.map(section => section.rewritten || section.content).join('\n\n');
    const blob = new Blob([new TextEncoder().encode(content)], { 
      type: 'text/plain;charset=utf-8' 
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `改写后的文档.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleSectionSelect = (sectionId: string | number, selected: boolean) => {
    if (selected) {
      setSelectedSections([...selectedSections, sectionId]);
    } else {
      setSelectedSections(selectedSections.filter(id => id !== sectionId));
    }
  };

  const handleShowImageGeneration = () => {
    setShowImageGeneration(true);
  };

  const handleShowImageGallery = () => {
    setShowImageGallery(true);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-800 mb-8 text-center">
            智能文案改写平台
          </h1>

          {/* Status Indicators */}
          <div className="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            {/* Database Status */}
            <div className={`flex items-center justify-center p-3 rounded-lg ${dbStatus.error ? 'bg-red-50 text-red-700' : 'bg-green-50 text-green-700'}`}>
              <Database className="h-4 w-4 mr-2" />
              <span className={`w-2 h-2 rounded-full mr-2 ${dbStatus.initialized ? 'bg-green-500' : 'bg-red-500'}`} />
              {databaseType === 'mongodb' ? 'MongoDB' : 'SQL.js'} - {dbStatus.initialized ? '已连接' : '未连接'}
            </div>

            {/* AI Service Status */}
            <div className={`flex items-center justify-center p-3 rounded-lg ${aiServiceStatus.primary || aiServiceStatus.fallback ? 'bg-blue-50 text-blue-700' : 'bg-yellow-50 text-yellow-700'}`}>
              {aiServiceStatus.primary || aiServiceStatus.fallback ? (
                <Wifi className="h-4 w-4 mr-2" />
              ) : (
                <WifiOff className="h-4 w-4 mr-2" />
              )}
              AI服务 - {aiServiceStatus.primary ? 'DeepSeek' : aiServiceStatus.fallback ? 'Ollama' : '不可用'}
            </div>

            {/* Image Generation Status */}
            <div className={`flex items-center justify-center p-3 rounded-lg ${imageSupported && aiServiceStatus.imageGeneration ? 'bg-purple-50 text-purple-700' : 'bg-gray-50 text-gray-500'}`}>
              <Image className="h-4 w-4 mr-2" />
              文生图 - {imageSupported && aiServiceStatus.imageGeneration ? '可用' : '不可用'}
            </div>
          </div>
          
          <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
            <DocumentList 
              documents={documents}
              onLoadDocument={loadDocument}
              onDeleteDocument={handleDeleteDocument}
            />

            <FileUpload 
              onFileUpload={handleFileUpload}
              loading={loading}
            />

            <SectionList
              sections={sections}
              selectedSections={selectedSections}
              loading={loading}
              onSectionSelect={handleSectionSelect}
              onRewrite={handleRewrite}
              onBatchRewrite={handleBatchRewrite}
              onDownload={handleDownload}
            />

            {/* Image Generation and Gallery Buttons */}
            {currentDocumentId && imageSupported && (
              <div className="mt-6 flex flex-wrap gap-3 justify-center">
                <button
                  type="button"
                  onClick={handleShowImageGeneration}
                  className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors flex items-center"
                >
                  <Wand2 className="h-4 w-4 mr-2" />
                  生成图片
                </button>
                <button
                  type="button"
                  onClick={handleShowImageGallery}
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center"
                >
                  <Image className="h-4 w-4 mr-2" />
                  图片画廊
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      <PromptModal 
        isOpen={showPromptModal}
        customPrompt={customPrompt}
        loading={loading}
        onClose={() => setShowPromptModal(false)}
        onPromptChange={setCustomPrompt}
        onConfirm={handleConfirmRewrite}
      />

      <DeleteConfirmModal
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={confirmDelete}
      />

      {/* Image Generation Modal */}
      {showImageGeneration && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="max-w-4xl w-full max-h-[90vh] overflow-auto">
            <ImageGeneration
              documentId={currentDocumentId?.toString()}
              onClose={() => setShowImageGeneration(false)}
            />
          </div>
        </div>
      )}

      {/* Image Gallery Modal */}
      {showImageGallery && currentDocumentId && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="max-w-6xl w-full max-h-[90vh] overflow-auto">
            <ImageGallery
              documentId={currentDocumentId.toString()}
              onClose={() => setShowImageGallery(false)}
            />
          </div>
        </div>
      )}
    </div>
  );
}

export default App;