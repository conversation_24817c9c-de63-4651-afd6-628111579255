// Lazy load SQL.js to reduce initial bundle size
const loadSqlJs = () => import('sql.js');

let db: any = null;
let dbChanged = false;
let dbStatus = { initialized: false, error: null };

// Initialize the SQL.js database with lazy loading
async function initDb() {
  if (!db) {
    dbStatus.initialized = false;
    dbStatus.error = null;
    
    try {
      // Try to load existing database from localStorage
      const savedDb = localStorage.getItem('rewriterDb');
      
      // Lazy load SQL.js
      const { default: initSqlJs } = await loadSqlJs();
      const SQL = await initSqlJs();
      
      db = savedDb
        ? new SQL.Database(new Uint8Array(JSON.parse(savedDb)))
        : new SQL.Database();
      
      // Initialize database schema
      db.run(`
        CREATE TABLE IF NOT EXISTS documents (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          title TEXT NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        CREATE TABLE IF NOT EXISTS sections (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          document_id INTEGER,
          content TEXT NOT NULL,
          rewritten TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (document_id) REFERENCES documents (id)
        );
      `);
      
      dbStatus.initialized = true;
    } catch (error) {
      dbStatus.error = error;
      throw error;
    }
  }
  return db;
}

// Save database to localStorage when changes occur
async function saveDb() {
  if (dbChanged && db) {
    try {
      const data = db.export();
      localStorage.setItem('rewriterDb', JSON.stringify(Array.from(data)));
      dbChanged = false;
    } catch (error) {
      console.error('Failed to save database:', error);
    }
  }
}

// Add window unload handler to save database
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    saveDb();
  });
  
  // Auto-save every 30 seconds
  setInterval(saveDb, 30000);
}

export interface Section {
  id: number;
  document_id: number;
  content: string;
  rewritten?: string;
}

export interface Document {
  id: number;
  title: string;
  created_at: string;
}

export async function createDocument(title: string): Promise<number> {
  const db = await initDb();
  db.run('INSERT INTO documents (title) VALUES (?)', [title]);
  const result = db.exec('SELECT last_insert_rowid()');
  dbChanged = true;
  saveDb(); // Save immediately after important operations
  return result[0].values[0][0];
}

export async function saveSections(documentId: number, sections: { content: string }[]): Promise<Section[]> {
  const db = await initDb();
  const savedSections: Section[] = [];
  
  // Use transaction for better performance
  db.run('BEGIN TRANSACTION');
  
  try {
    for (const section of sections) {
      db.run('INSERT INTO sections (document_id, content) VALUES (?, ?)', [documentId, section.content]);
      const result = db.exec('SELECT last_insert_rowid()');
      const id = result[0].values[0][0];
      
      savedSections.push({
        id,
        document_id: documentId,
        content: section.content
      });
    }
    
    db.run('COMMIT');
    dbChanged = true;
    saveDb();
  } catch (error) {
    db.run('ROLLBACK');
    throw error;
  }

  return savedSections;
}

export async function updateSection(id: number, rewritten: string): Promise<void> {
  const db = await initDb();
  db.run('UPDATE sections SET rewritten = ? WHERE id = ?', [rewritten, id]);
  dbChanged = true;
  saveDb();
}

export async function getSections(documentId: number): Promise<Section[]> {
  const db = await initDb();
  const result = db.exec('SELECT * FROM sections WHERE document_id = ? ORDER BY id ASC', [documentId]);
  
  if (!result.length) return [];
  
  return result[0].values.map((row: any[]) => ({
    id: row[0],
    document_id: row[1],
    content: row[2],
    rewritten: row[3]
  }));
}

export async function getLatestDocument(): Promise<Section[]> {
  const db = await initDb();
  const result = db.exec(`
    SELECT s.* FROM sections s
    JOIN documents d ON s.document_id = d.id
    ORDER BY d.created_at DESC, s.id ASC
    LIMIT 1
  `);
  
  if (!result.length) return [];
  
  return result[0].values.map((row: any[]) => ({
    id: row[0],
    document_id: row[1],
    content: row[2],
    rewritten: row[3]
  }));
}

export async function getAllDocuments(): Promise<Document[]> {
  const db = await initDb();
  const result = db.exec('SELECT * FROM documents ORDER BY created_at DESC');
  
  if (!result.length) return [];
  
  return result[0].values.map((row: any[]) => ({
    id: row[0],
    title: row[1],
    created_at: row[2]
  }));
}

export function getDbStatus() {
  return dbStatus;
}

export async function deleteDocument(id: number): Promise<void> {
  const db = await initDb();
  
  // Use transaction for data consistency
  db.run('BEGIN TRANSACTION');
  
  try {
    // Delete sections first due to foreign key constraint
    db.run('DELETE FROM sections WHERE document_id = ?', [id]);
    db.run('DELETE FROM documents WHERE id = ?', [id]);
    db.run('COMMIT');
    
    dbChanged = true;
    saveDb();
  } catch (error) {
    db.run('ROLLBACK');
    throw error;
  }
}