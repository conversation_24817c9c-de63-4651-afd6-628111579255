import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  build: {
    // Enable code splitting
    rollupOptions: {
      output: {
        manualChunks: {
          // Split vendor libraries into separate chunks
          'react-vendor': ['react', 'react-dom'],
          'icons': ['lucide-react'],
          'db-sqljs': ['sql.js'],
          'db-mongodb': ['mongodb', 'mongoose'],
          'file-processing': ['mammoth', 'xlsx'],
        },
      },
    },
    // Reduce chunk size warning threshold
    chunkSizeWarningLimit: 600,
    // Enable source maps for debugging
    sourcemap: true,
    // Optimize CSS
    cssCodeSplit: true,
  },
  optimizeDeps: {
    exclude: ['lucide-react', 'sql.js', 'mongodb', 'mongoose'],
    include: ['react', 'react-dom'],
  },
  // Configure server for better development experience
  server: {
    open: true,
    port: 3000,
  },
  // Enable tree shaking
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV),
  },
});
