// Lazy load heavy file processing libraries
const loadMammoth = () => import('mammoth');
const loadXLSX = () => import('xlsx');

export async function processFile(file: File): Promise<string> {
  const filename = file.name.toLowerCase();
  let content = '';

  if (filename.endsWith('.docx') || filename.endsWith('.doc') || filename.endsWith('.wps')) {
    // Handle Word documents - lazy load mammoth
    const mammoth = await loadMammoth();
    const arrayBuffer = await file.arrayBuffer();
    const result = await mammoth.extractRawText({ arrayBuffer });
    content = result.value;
  } else if (filename.endsWith('.xlsx') || filename.endsWith('.xls') || filename.endsWith('.et')) {
    // Handle Excel files - lazy load xlsx
    const XLSX = await loadXLSX();
    const arrayBuffer = await file.arrayBuffer();
    const workbook = XLSX.read(arrayBuffer);
    content = workbook.SheetNames
      .map(sheetName => {
        const sheet = workbook.Sheets[sheetName];
        return XLSX.utils.sheet_to_txt(sheet);
      })
      .join('\n\n');
  } else if (filename.endsWith('.pptx') || filename.endsWith('.ppt') || filename.endsWith('.dps')) {
    // For PowerPoint files, we'll need to inform the user about limited support
    alert('注意：PPT文件目前仅支持提取文本内容，可能会丢失部分格式。');
    const reader = new FileReader();
    content = await new Promise((resolve, reject) => {
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = reject;
      reader.readAsText(file);
    });
  }

  return content;
}

export function processContent(content: string): string[] {
  // First, clean up the content
  const cleanContent = content
    .replace(/[\ufffd\ufeff\u2028\u2029]/g, '') // Remove special characters
    .replace(/\r\n/g, '\n') // Normalize line endings
    .replace(/\n{3,}/g, '\n\n') // Normalize multiple line breaks
    .trim();

  // Split into potential paragraphs
  const rawParagraphs = cleanContent.split(/\n\s*\n/);
  
  // Merge short fragments and process paragraphs
  const paragraphs = [];
  let currentParagraph = '';
  
  for (const para of rawParagraphs) {
    const trimmedPara = para.trim();
    
    // Skip empty paragraphs
    if (!trimmedPara) continue;
    
    // If it's a very short fragment (less than 10 characters) and not ending with period-like punctuation,
    // append it to the current paragraph
    if (trimmedPara.length < 10 && 
        !trimmedPara.match(/[。！？.!?]$/) && 
        currentParagraph) {
      currentParagraph += ' ' + trimmedPara;
    }
    // If it's a continuation of a sentence (starts with lowercase or Chinese punctuation)
    else if (currentParagraph && 
             (trimmedPara.match(/^[a-z,，、]/) || 
              trimmedPara.match(/^[：，、；]|[^。！？.!?]$/))) {
      currentParagraph += ' ' + trimmedPara;
    }
    // Start a new paragraph
    else {
      if (currentParagraph) {
        paragraphs.push(currentParagraph);
      }
      currentParagraph = trimmedPara;
    }
  }
  
  // Add the last paragraph if exists
  if (currentParagraph) {
    paragraphs.push(currentParagraph);
  }

  return paragraphs;
}